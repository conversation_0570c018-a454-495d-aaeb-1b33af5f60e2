using System;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace CinemaBooking.Services
{
    public class EmailService
    {
        private readonly IConfiguration _configuration;
        
        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        
        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body)
        {
            try
            {
                // L<PERSON>y thông tin cấu hình email từ appsettings.json
                var emailSettings = _configuration.GetSection("EmailSettings");
                var fromEmail = emailSettings["Email"];
                var password = emailSettings["Password"];
                var smtpServer = emailSettings["SmtpServer"];
                var port = int.Parse(emailSettings["Port"]);
                
                Console.WriteLine($"Chuẩn bị gửi email đến: {toEmail} từ {fromEmail} qua {smtpServer}:{port}");
                
                using (var client = new SmtpClient(smtpServer, port))
                {
                    client.UseDefaultCredentials = false;
                    client.Credentials = new NetworkCredential(fromEmail, password);
                    client.EnableSsl = true;
                    
                    // Thêm timeout dài hơn
                    client.Timeout = 30000; // 30 giây
                    
                    var mailMessage = new MailMessage
                    {
                        From = new MailAddress(fromEmail, "CinemaBooking"),
                        Subject = subject,
                        Body = body,
                        IsBodyHtml = true
                    };
                    
                    mailMessage.To.Add(toEmail);
                    
                    Console.WriteLine("Đang gửi email...");
                    await client.SendMailAsync(mailMessage);
                    Console.WriteLine("Đã gửi email thành công!");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chi tiết lỗi gửi email: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                return false;
            }
        }
        
        public async Task<bool> SendOtpEmailAsync(string toEmail, string otp, string fullName = null)
        {
            var name = string.IsNullOrEmpty(fullName) ? "Quý khách" : fullName;
            
            var subject = "Mã xác thực đăng ký tài khoản CinemaBooking";
            var body = $@"
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }}
                    .header {{ background-color: #4a148c; color: white; padding: 10px; text-align: center; border-radius: 5px 5px 0 0; }}
                    .content {{ padding: 20px; }}
                    .otp-code {{ font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px; color: #4a148c; }}
                    .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>Xác thực tài khoản CinemaBooking</h2>
                    </div>
                    <div class='content'>
                        <p>Xin chào {name},</p>
                        <p>Cảm ơn bạn đã đăng ký tài khoản tại CinemaBooking. Để hoàn tất quá trình đăng ký, vui lòng sử dụng mã xác thực dưới đây:</p>
                        
                        <div class='otp-code'>{otp}</div>
                        
                        <p>Mã xác thực có hiệu lực trong vòng 10 phút.</p>
                        <p>Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có bất kỳ câu hỏi nào.</p>
                        <p>Trân trọng,<br>Đội ngũ CinemaBooking</p>
                    </div>
                    <div class='footer'>
                        <p>© 2025 CinemaBooking. Tất cả các quyền được bảo lưu.</p>
                    </div>
                </div>
            </body>
            </html>";
            
            return await SendEmailAsync(toEmail, subject, body);
        }
        
        public async Task<bool> SendForgotPasswordOtpAsync(string toEmail, string otp, string fullName = null)
        {
            var name = string.IsNullOrEmpty(fullName) ? "Quý khách" : fullName;
            
            var subject = "Mã xác thực khôi phục mật khẩu CinemaBooking";
            var body = $@"
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }}
                    .header {{ background-color: #4a148c; color: white; padding: 10px; text-align: center; border-radius: 5px 5px 0 0; }}
                    .content {{ padding: 20px; }}
                    .otp-code {{ font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px; color: #4a148c; }}
                    .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>Khôi phục mật khẩu CinemaBooking</h2>
                    </div>
                    <div class='content'>
                        <p>Xin chào {name},</p>
                        <p>Chúng tôi nhận được yêu cầu khôi phục mật khẩu cho tài khoản CinemaBooking của bạn. Vui lòng sử dụng mã xác thực dưới đây để tiếp tục quá trình:</p>
                        
                        <div class='otp-code'>{otp}</div>
                        
                        <p>Mã xác thực có hiệu lực trong vòng 10 phút.</p>
                        <p>Nếu bạn không yêu cầu khôi phục mật khẩu, vui lòng bỏ qua email này và kiểm tra lại tài khoản của bạn.</p>
                        <p>Trân trọng,<br>Đội ngũ CinemaBooking</p>
                    </div>
                    <div class='footer'>
                        <p>© 2025 CinemaBooking. Tất cả các quyền được bảo lưu.</p>
                    </div>
                </div>
            </body>
            </html>";
            
            return await SendEmailAsync(toEmail, subject, body);
        }
    }
} 