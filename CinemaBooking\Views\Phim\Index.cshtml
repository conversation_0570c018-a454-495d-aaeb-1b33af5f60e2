@model IEnumerable<CinemaBooking.Models.Phim>

@{
    ViewData["Title"] = "Quản lý phim";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Quản lý phim</h2>
        @if (User.IsInRole("Admin"))
        {
            <a asp-action="Create" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> Thêm phim mới
            </a>
        }
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Mã phim</th>
                    <th>Tên phim</th>
                    <th>Thể loại</th>
                    <th>Thời lượng</th>
                    <th><PERSON><PERSON><PERSON> ph<PERSON><PERSON> hành</th>
                    <th><PERSON><PERSON><PERSON> dạng</th>
                    <th><PERSON><PERSON> t<PERSON></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {
                    <tr>
                        <td>@item.MaPhim</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="@(string.IsNullOrEmpty(item.UrlPoster) ? "/images/no-image.jpg" : item.UrlPoster)" 
                                     class="me-2" alt="@item.TenPhim" style="width: 50px; height: 75px; object-fit: cover;">
                                <span>@item.TenPhim</span>
                            </div>
                        </td>
                        <td>@item.TheLoai</td>
                        <td>@item.ThoiLuong phút</td>
                        <td>@item.NgayPhatHanh?.ToString("dd/MM/yyyy")</td>
                        <td>@item.DinhDang</td>
                        <td>
                            <div class="btn-group">
                                <a asp-action="Detail" asp-route-id="@item.MaPhim" class="btn btn-info btn-sm">
                                    <i class="bi bi-info-circle"></i> Chi tiết
                                </a>
                                @if (User.IsInRole("Admin"))
                                {
                                    <a asp-action="Edit" asp-route-id="@item.MaPhim" class="btn btn-warning btn-sm">
                                        <i class="bi bi-pencil"></i> Sửa
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.MaPhim" class="btn btn-danger btn-sm">
                                        <i class="bi bi-trash"></i> Xóa
                                    </a>
                                }
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div> 