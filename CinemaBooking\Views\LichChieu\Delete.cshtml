@model CinemaBooking.Models.LichChieu

@{
    ViewData["Title"] = "Xóa lịch chiếu";
}

<div class="container-fluid">
    <h1 class="mt-4"><PERSON><PERSON>a lịch chiếu</h1>
    
    <div class="mb-3">
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại danh sách
        </a>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Xác nhận xóa lịch chiếu
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-circle"></i> Bạn có chắc chắn muốn xóa lịch chiếu này?</h5>
                <p>Hành động này không thể hoàn tác. Nếu đã có người đặt vé cho lịch chiếu này, bạn sẽ không thể xóa.</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Thông tin phim</h5>
                    <dl class="row">
                        <dt class="col-sm-4">Tên phim:</dt>
                        <dd class="col-sm-8">@Model.Phim?.TenPhim</dd>
                        
                        <dt class="col-sm-4">Thời lượng:</dt>
                        <dd class="col-sm-8">@Model.Phim?.ThoiLuong phút</dd>
                        
                        <dt class="col-sm-4">Thể loại:</dt>
                        <dd class="col-sm-8">@Model.Phim?.TheLoai</dd>
                    </dl>
                </div>
                
                <div class="col-md-6">
                    <h5>Thông tin lịch chiếu</h5>
                    <dl class="row">
                        <dt class="col-sm-4">Mã lịch chiếu:</dt>
                        <dd class="col-sm-8">@Model.MaLichChieu</dd>
                        
                        <dt class="col-sm-4">Rạp:</dt>
                        <dd class="col-sm-8">@Model.PhongChieu?.RapPhim?.TenRap</dd>
                        
                        <dt class="col-sm-4">Phòng:</dt>
                        <dd class="col-sm-8">@Model.PhongChieu?.SoPhong</dd>
                        
                        <dt class="col-sm-4">Ngày chiếu:</dt>
                        <dd class="col-sm-8">@Model.NgayChieu.ToString("dd/MM/yyyy")</dd>
                        
                        <dt class="col-sm-4">Giờ chiếu:</dt>
                        <dd class="col-sm-8">@Model.GioChieu.ToString(@"hh\:mm")</dd>
                        
                        <dt class="col-sm-4">Giá vé:</dt>
                        <dd class="col-sm-8">@Model.GiaVe.ToString("N0") VNĐ</dd>
                    </dl>
                </div>
            </div>
            
            <form asp-action="Delete" method="post" class="mt-4 text-center">
                <input type="hidden" asp-for="MaLichChieu" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash-alt"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Hủy
                </a>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Load FontAwesome nếu chưa có
            if (!$('link[href*="fontawesome"]').length) {
                $('head').append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">');
            }
        });
    </script>
} 