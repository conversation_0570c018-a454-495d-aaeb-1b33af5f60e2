# CinemaBooking - <PERSON><PERSON> thống đặt vé xem phim

CinemaBooking là một ứng dụng web quản lý rạp chiếu phim và đặt vé trực tuyến, đ<PERSON><PERSON><PERSON> phát triển bằng ASP.NET Core MVC. Hệ thống cho phép người dùng xem thông tin phim, đặt vé, thanh toán trực tuyến và quản lý thông tin cá nhân. <PERSON><PERSON><PERSON> thời, hệ thống cung cấp các chức năng quản lý toàn diện cho quản trị viên.

## Tính năng chính

### Dành cho người dùng
- Xem danh sách phim đang chiếu và sắp chiếu
- Xem chi tiết phim (thông tin, lịch chiếu, đánh giá)
- Đặt vé xem phim (chọn lịch chiếu, chọ<PERSON> ghế)
- <PERSON><PERSON> toán trực tuyến qua MoMo hoặc thanh toán tại rạp
- <PERSON>em lịch sử đặt vé
- <PERSON><PERSON><PERSON> gi<PERSON> phim sau khi xem
- <PERSON><PERSON><PERSON> ký, đ<PERSON><PERSON> nh<PERSON><PERSON> (bao gồm đăng nhập bằng Google)
- Quên mật khẩu và xác thực OTP qua email

### Dành cho quản trị viên
- Quản lý phim (thêm, sửa, xóa)
- Quản lý rạp phim và phòng chiếu
- Quản lý lịch chiếu
- Quản lý đặt vé và thanh toán
- Kiểm tra vé (quét mã QR)
- Quản lý người dùng và phân quyền
- Quản lý khuyến mãi
- Xem báo cáo và lịch sử giao dịch

## Công nghệ sử dụng

- **Framework**: ASP.NET Core MVC (.NET 9.0)
- **Database**: SQL Server với Entity Framework Core (Code First)
- **Authentication**: ASP.NET Core Identity, Google Authentication
- **Payment Integration**: MoMo, VNPay (sandbox)
- **Email Service**: SMTP (Gmail)
- **Frontend**: HTML, CSS, JavaScript, Bootstrap 5
- **Other**: QR Code generation, File upload

## Cấu trúc dự án

- **Controllers/**: Chứa các controller xử lý logic của ứng dụng
- **Models/**: Chứa các model và entity classes
- **Views/**: Chứa các file giao diện người dùng
- **Data/**: Chứa ApplicationDbContext và cấu hình database
- **Migrations/**: Chứa các migration để quản lý schema database
- **Services/**: Chứa các service như EmailService, MomoService, OtpService
- **wwwroot/**: Chứa các file tĩnh (CSS, JavaScript, images)
- **uploads/**: Chứa các file được upload (poster phim, trailer)

## Cài đặt và chạy dự án

### Yêu cầu hệ thống
- .NET 9.0 SDK
- SQL Server
- Visual Studio 2022 hoặc Visual Studio Code

### Các bước cài đặt

1. Clone repository:
```
git clone <repository-url>
```

2. Cấu hình chuỗi kết nối database trong file `appsettings.json`:
```json
"ConnectionStrings": {
  "DefaultConnection": "Server=YOUR_SERVER;Database=cinema_booking;Trusted_Connection=True;TrustServerCertificate=True;"
}
```

3. Mở terminal và chạy các lệnh sau để tạo database:
```
dotnet ef database update
```

4. Chạy ứng dụng:
```
dotnet run
```

5. Truy cập ứng dụng tại địa chỉ: `http://localhost:5153`

## Cấu hình thanh toán

### MoMo
Cấu hình thông tin MoMo trong file `appsettings.json`:
```json
"Momo": {
  "PartnerCode": "YOUR_PARTNER_CODE",
  "AccessKey": "YOUR_ACCESS_KEY",
  "SecretKey": "YOUR_SECRET_KEY",
  "Endpoint": "https://test-payment.momo.vn/v2/gateway/api/create"
}
```

### VNPay
Cấu hình thông tin VNPay trong file `appsettings.json`:
```json
"VnPay": {
  "TmnCode": "YOUR_TMN_CODE",
  "HashSecret": "YOUR_HASH_SECRET",
  "BaseUrl": "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html",
  "Command": "pay",
  "CurrCode": "VND",
  "Version": "2.1.0",
  "Locale": "vn"
}
```

## Cấu hình Email
Cấu hình thông tin email trong file `appsettings.json`:
```json
"EmailSettings": {
  "Email": "YOUR_EMAIL",
  "Password": "YOUR_APP_PASSWORD",
  "SmtpServer": "smtp.gmail.com",
  "Port": 587
}
```

## Tài khoản mặc định

Sau khi chạy ứng dụng lần đầu, hệ thống sẽ tạo các tài khoản mặc định:

- **Admin**:
  - Username: admin
  - Password: Admin@123

- **User**:
  - Username: user
  - Password: User@123

## Đóng góp

Nếu bạn muốn đóng góp vào dự án, vui lòng tạo pull request hoặc báo cáo lỗi qua mục Issues.

## Giấy phép

Dự án này được phân phối dưới giấy phép MIT. Xem file `LICENSE` để biết thêm chi tiết.
