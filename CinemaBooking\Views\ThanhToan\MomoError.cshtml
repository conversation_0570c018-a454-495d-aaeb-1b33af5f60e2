@{
    ViewData["Title"] = "Lỗi thanh toán MoMo";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">Không thể kết nối đến <PERSON></h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <img src="~/images/momo-logo.png" alt="MoMo" height="60" class="mb-3" />
                        <h4 class="text-danger">Đ<PERSON> xảy ra lỗi khi kết nối đến <PERSON></h4>
                        <p class="text-muted">@ViewBag.ErrorMessage</p>
                    </div>

                    <div class="alert alert-info">
                        <h5>Nguyên nhân có thể:</h5>
                        <ul>
                            <li>Kế<PERSON> nối internet không ổn định</li>
                            <li>M<PERSON>y chủ MoMo tạm thời không phản hồi</li>
                            <li>Thông tin thanh toán không hợp lệ</li>
                            <li>Vấn đề xử lý dữ liệu từ MoMo</li>
                        </ul>
                    </div>

                    <div class="alert alert-primary">
                        <h5>Bạn có thể thử các cách sau:</h5>
                        <ul>
                            <li>Kiểm tra kết nối internet</li>
                            <li>Thử lại sau vài phút</li>
                            <li>Chọn phương thức thanh toán khác</li>
                            <li>Liên hệ với rạp phim để được hỗ trợ</li>
                        </ul>
                    </div>

                    <div class="d-flex flex-column flex-md-row justify-content-center mt-4">
                        <a href="@Url.Action("Index", "ThanhToan", new { maDatVe = ViewBag.MaDatVe })" class="btn btn-primary mb-2 mb-md-0 me-md-2">
                            <i class="fas fa-redo me-1"></i> Thử lại
                        </a>
                        <a href="@Url.Action("LichSuDatVe", "Home")" class="btn btn-secondary mb-2 mb-md-0 me-md-2">
                            <i class="fas fa-history me-1"></i> Lịch sử đặt vé
                        </a>
                        <a href="@Url.Action("MomoSandboxGuide", "Home")" class="btn btn-info">
                            <i class="fas fa-book me-1"></i> Xem hướng dẫn
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 