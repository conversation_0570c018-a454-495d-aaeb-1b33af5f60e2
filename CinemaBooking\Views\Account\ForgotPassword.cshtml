@model CinemaBooking.Models.ViewModels.ForgotPasswordViewModel

@{
    ViewData["Title"] = "Quên mật khẩu";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="auth-section">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h4>Kh<PERSON>i phục mật khẩu</h4>
                <p><PERSON><PERSON> lòng làm theo các bước để khôi phục mật khẩu của bạn</p>
            </div>
            <div class="auth-body">
                @if (!ViewData.ModelState.IsValid && ViewData.ModelState[""] != null && ViewData.ModelState[""].Errors.Count > 0)
                {
                    <div class="alert alert-danger mb-3">
                        <span>@ViewData.ModelState[""].Errors[0].ErrorMessage</span>
                    </div>
                }
                
                @if (TempData["OtpMessage"] != null)
                {
                    <div class="alert alert-info mb-3">
                        <span>@TempData["OtpMessage"]</span>
                    </div>
                }
                
                <div class="debug-info" style="display: none;">
                    <p>Bước hiện tại: @Model.Buoc</p>
                    <p>Đã gửi OTP: @Model.DaGuiOTP</p>
                    <p>Email: @Model.Email</p>
                    <p>MaXacThuc: @(string.IsNullOrEmpty(Model.MaXacThuc) ? "chưa nhập" : Model.MaXacThuc)</p>
                </div>
                
                <form asp-action="ForgotPassword" method="post" class="auth-form">
                    <!-- Đảm bảo giá trị Buoc được truyền đúng khi form submit -->
                    <input type="hidden" name="Buoc" value="@Model.Buoc" />
                    <input type="hidden" name="DaGuiOTP" value="@Model.DaGuiOTP.ToString().ToLower()" />
                    
                    @if (Model.Buoc == 1)
                    {
                        <!-- BƯỚC 1: NHẬP EMAIL -->
                        <div class="form-floating mb-3">
                            <input type="email" asp-for="Email" class="form-control" id="floatingEmail" placeholder=" " required />
                            <label for="floatingEmail">Email đăng ký</label>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>
                        
                        <button type="submit" class="btn-auth-submit">
                            Tiếp tục
                        </button>
                    }
                    else if (Model.Buoc == 2)
                    {
                        <!-- BƯỚC 2: NHẬP MÃ XÁC THỰC -->
                        <input type="hidden" name="Email" value="@Model.Email" />
                        
                        <div class="form-floating mb-3">
                            <input type="text" readonly value="@Model.Email" class="form-control bg-light" id="displayEmail" />
                            <label for="displayEmail">Email đăng ký</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" asp-for="MaXacThuc" class="form-control" id="floatingOtp" placeholder=" " required autocomplete="off" />
                            <label for="floatingOtp">Mã xác thực</label>
                            <span asp-validation-for="MaXacThuc" class="text-danger"></span>
                            <small class="form-text">Vui lòng nhập mã xác thực được gửi đến email của bạn</small>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <a asp-action="ResendForgotPasswordOtp" class="btn-link">Gửi lại mã</a>
                        </div>
                        
                        <button type="submit" class="btn-auth-submit">
                            Xác thực
                        </button>
                    }
                    else if (Model.Buoc == 3)
                    {
                        <!-- BƯỚC 3: ĐẶT MẬT KHẨU MỚI -->
                        <input type="hidden" name="Email" value="@Model.Email" />
                        
                        <div class="form-floating mb-3">
                            <input type="text" readonly value="@Model.Email" class="form-control bg-light" id="displayEmail" />
                            <label for="displayEmail">Email đăng ký</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" asp-for="MatKhauMoi" class="form-control" id="floatingPassword" placeholder=" " required />
                            <label for="floatingPassword">Mật khẩu mới</label>
                            <span asp-validation-for="MatKhauMoi" class="text-danger"></span>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" asp-for="XacNhanMatKhauMoi" class="form-control" id="floatingConfirmPassword" placeholder=" " required />
                            <label for="floatingConfirmPassword">Xác nhận mật khẩu mới</label>
                            <span asp-validation-for="XacNhanMatKhauMoi" class="text-danger"></span>
                        </div>
                        
                        <button type="submit" class="btn-auth-submit">
                            Cập nhật mật khẩu
                        </button>
                    }
                </form>
            </div>
            <div class="auth-footer">
                <p>Đã nhớ mật khẩu? <a asp-action="Login">Đăng nhập ngay</a></p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Hiển thị debug info khi bấm tổ hợp phím Ctrl+Shift+D
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                const debugInfo = document.querySelector('.debug-info');
                debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
            }
        });
        
        // Bật debug để hiển thị các lỗi
        console.log('Form data:', {
            buoc: @Model.Buoc,
            daGuiOTP: @(Model.DaGuiOTP ? "true" : "false"),
            email: '@Model.Email'
        });
    </script>
} 