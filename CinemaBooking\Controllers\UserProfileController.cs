using System;
using System.Threading.Tasks;
using System.Security.Claims;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using CinemaBooking.Models;
using CinemaBooking.Data;
using CinemaBooking.Models.ViewModels;

namespace CinemaBooking.Controllers
{
    [Authorize]
    public class UserProfileController : Controller
    {
        private readonly ApplicationDbContext _context;

        public UserProfileController(
            ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var userId = User.FindFirstValue("MaNguoiDung");
            if (string.IsNullOrEmpty(userId) || !int.TryParse(userId, out int userIdInt))
            {
                return RedirectToAction("Login", "Account");
            }

            var user = await _context.NguoiDungs.FirstOrDefaultAsync(u => u.MaNguoiDung == userIdInt);
            if (user == null)
            {
                return NotFound();
            }

            // Thiết lập vai trò người dùng
            ViewBag.Roles = new List<string>();
            if (user.MaVaiTro.HasValue)
            {
                if (user.MaVaiTro == 1)
                {
                    ViewBag.Roles.Add("Admin");
                }
                else if (user.MaVaiTro == 2)
                {
                    ViewBag.Roles.Add("Thành viên");
                }
            }

            return View(user);
        }

        public async Task<IActionResult> Edit()
        {
            var userId = User.FindFirstValue("MaNguoiDung");
            if (string.IsNullOrEmpty(userId) || !int.TryParse(userId, out int userIdInt))
            {
                return RedirectToAction("Login", "Account");
            }

            var user = await _context.NguoiDungs.FirstOrDefaultAsync(u => u.MaNguoiDung == userIdInt);
            if (user == null)
            {
                return NotFound();
            }

            var model = new UserProfileViewModel
            {
                HoTen = user.HoTen,
                Email = user.Email,
                PhoneNumber = user.SoDienThoai
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(UserProfileViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var userId = User.FindFirstValue("MaNguoiDung");
            if (string.IsNullOrEmpty(userId) || !int.TryParse(userId, out int userIdInt))
            {
                return RedirectToAction("Login", "Account");
            }

            var user = await _context.NguoiDungs.FirstOrDefaultAsync(u => u.MaNguoiDung == userIdInt);
            if (user == null)
            {
                return NotFound();
            }

            // Kiểm tra trùng email
            if (user.Email != model.Email)
            {
                var existingUser = await _context.NguoiDungs.FirstOrDefaultAsync(u => u.Email == model.Email && u.MaNguoiDung != userIdInt);
                if (existingUser != null)
                {
                    ModelState.AddModelError("Email", "Email này đã được sử dụng");
                    return View(model);
                }
            }

            // Cập nhật thông tin
            user.HoTen = model.HoTen;
            user.Email = model.Email;
            user.SoDienThoai = model.PhoneNumber;

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Cập nhật thông tin thành công";
            return RedirectToAction(nameof(Index));
        }

        public IActionResult ChangePassword()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var userId = User.FindFirstValue("MaNguoiDung");
            if (string.IsNullOrEmpty(userId) || !int.TryParse(userId, out int userIdInt))
            {
                return RedirectToAction("Login", "Account");
            }

            var user = await _context.NguoiDungs.FirstOrDefaultAsync(u => u.MaNguoiDung == userIdInt);
            if (user == null)
            {
                return NotFound();
            }

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(model.CurrentPassword, user.MatKhau))
            {
                ModelState.AddModelError("CurrentPassword", "Mật khẩu hiện tại không chính xác");
                return View(model);
            }

            // Set new password
            user.MatKhau = BCrypt.Net.BCrypt.HashPassword(model.NewPassword);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Đổi mật khẩu thành công";
            return RedirectToAction(nameof(Index));
        }
    }
}