@model CinemaBooking.Models.NguoiDung

@{
    ViewData["Title"] = "Thông tin tài khoản";
}

<div class="container py-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Thông tin tài khoản</h4>
                </div>
                <div class="card-body">
                    <nav class="nav flex-column">
                        <a class="nav-link active" asp-action="Index" asp-controller="UserProfile">
                            <i class="bi bi-person"></i> Hồ sơ cá nhân
                        </a>
                        <a class="nav-link" asp-action="Edit" asp-controller="UserProfile">
                            <i class="bi bi-pencil-square"></i> Chỉnh sửa thông tin
                        </a>
                        <a class="nav-link" asp-action="ChangePassword" asp-controller="UserProfile">
                            <i class="bi bi-key"></i> <PERSON><PERSON><PERSON> m<PERSON> khẩ<PERSON>
                        </a>
                        <a class="nav-link" asp-action="LichSuDatVe" asp-controller="Home">
                            <i class="bi bi-ticket-perforated"></i> Lịch sử đặt vé
                        </a>
                    </nav>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Hồ sơ cá nhân</h4>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <div class="row mb-3">
                        <div class="col-md-4 text-md-end fw-bold">
                            <label>Tên đăng nhập:</label>
                        </div>
                        <div class="col-md-8">
                            <span>@Model.TenDangNhap</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 text-md-end fw-bold">
                            <label>Họ và tên:</label>
                        </div>
                        <div class="col-md-8">
                            <span>@(string.IsNullOrEmpty(Model.HoTen) ? "Chưa cập nhật" : Model.HoTen)</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 text-md-end fw-bold">
                            <label>Email:</label>
                        </div>
                        <div class="col-md-8">
                            <span>@Model.Email</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 text-md-end fw-bold">
                            <label>Số điện thoại:</label>
                        </div>
                        <div class="col-md-8">
                            <span>@(string.IsNullOrEmpty(Model.SoDienThoai) ? "Chưa cập nhật" : Model.SoDienThoai)</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 text-md-end fw-bold">
                            <label>Vai trò:</label>
                        </div>
                        <div class="col-md-8">
                            @if (ViewBag.Roles != null && ViewBag.Roles.Count > 0)
                            {
                                <span>@string.Join(", ", ViewBag.Roles)</span>
                            }
                            else
                            {
                                <span>Thành viên</span>
                            }
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 text-md-end fw-bold">
                            <label>Ngày tạo tài khoản:</label>
                        </div>
                        <div class="col-md-8">
                            <span>@(Model.NgayTao?.ToString("dd/MM/yyyy HH:mm") ?? "Không xác định")</span>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <a asp-action="Edit" class="btn btn-primary">
                            <i class="bi bi-pencil-square"></i> Chỉnh sửa thông tin
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .nav-link {
            color: #333;
            padding: 0.75rem 1rem;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }

        .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
    </style>
}