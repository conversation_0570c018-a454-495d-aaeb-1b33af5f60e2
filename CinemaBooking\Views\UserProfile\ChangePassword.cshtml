@model CinemaBooking.Models.ViewModels.ChangePasswordViewModel

@{
    ViewData["Title"] = "Đổi mật khẩu";
}

<div class="container py-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Thông tin tài khoản</h4>
                </div>
                <div class="card-body">
                    <nav class="nav flex-column">
                        <a class="nav-link" asp-action="Index" asp-controller="UserProfile">
                            <i class="bi bi-person"></i> <PERSON>ồ sơ cá nhân
                        </a>
                        <a class="nav-link" asp-action="Edit" asp-controller="UserProfile">
                            <i class="bi bi-pencil-square"></i> Chỉnh sửa thông tin
                        </a>
                        <a class="nav-link active" asp-action="ChangePassword" asp-controller="UserProfile">
                            <i class="bi bi-key"></i> <PERSON><PERSON><PERSON> mật khẩu
                        </a>
                        <a class="nav-link" asp-action="LichSuDatVe" asp-controller="Home">
                            <i class="bi bi-ticket-perforated"></i> Lịch sử đặt vé
                        </a>
                    </nav>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Đổi mật khẩu</h4>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <form asp-action="ChangePassword" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="mb-3 row">
                            <label asp-for="CurrentPassword" class="col-md-4 col-form-label text-md-end"></label>
                            <div class="col-md-8">
                                <input asp-for="CurrentPassword" class="form-control" />
                                <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <label asp-for="NewPassword" class="col-md-4 col-form-label text-md-end"></label>
                            <div class="col-md-8">
                                <input asp-for="NewPassword" class="form-control" />
                                <span asp-validation-for="NewPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <label asp-for="ConfirmPassword" class="col-md-4 col-form-label text-md-end"></label>
                            <div class="col-md-8">
                                <input asp-for="ConfirmPassword" class="form-control" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-8 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Đổi mật khẩu
                                </button>
                                <a asp-action="Index" class="btn btn-outline-secondary ms-2">
                                    <i class="bi bi-x-circle"></i> Hủy
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .nav-link {
            color: #333;
            padding: 0.75rem 1rem;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }

        .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 