/**
 * Effects CSS - <PERSON><PERSON><PERSON> và animation
 */

/* <PERSON><PERSON><PERSON>ng <PERSON>eal khi scroll */
.reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.reveal.active {
    opacity: 1;
    transform: translateY(0);
}

/* Delay cho các phần tử reveal */
.reveal-delay-1 {
    transition-delay: 0.1s;
}

.reveal-delay-2 {
    transition-delay: 0.2s;
}

.reveal-delay-3 {
    transition-delay: 0.3s;
}

.reveal-delay-4 {
    transition-delay: 0.4s;
}

.reveal-delay-5 {
    transition-delay: 0.5s;
}

/* Hiệu ứng Pulse cho nút */
.btn-pulse {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.pulse-animation {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(229, 9, 20, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(229, 9, 20, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(229, 9, 20, 0);
    }
}

/* <PERSON><PERSON><PERSON>ng Ripple cho nút */
.btn-ripple {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    transform: translateZ(0);
}

.btn-ripple:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.btn-ripple:active {
    transform: translateY(1px);
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
    transform: scale(0);
    opacity: 1;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Hiệu ứng nút chính */
.btn-danger, .btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 1;
}

.btn-danger::after, .btn-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: -1;
}

.btn-danger:hover::after, .btn-primary:hover::after {
    transform: translateX(0);
}

/* Hiệu ứng hover cho movie card */
.movie-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    will-change: transform;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transform-style: preserve-3d;
    perspective: 1000px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.movie-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Hiệu ứng shine cho movie card */
.movie-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: skewX(-25deg);
    z-index: 1;
    transition: left 0.7s ease;
    pointer-events: none;
}

.movie-card:hover::before {
    left: 150%;
}

/* Hiệu ứng ánh sáng cho movie card */
.card-glare {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 2;
}

.movie-card:hover .card-glare {
    opacity: 1;
}

/* Hiệu ứng cho card content */
.card-content {
    padding: 15px;
    transition: transform 0.3s ease;
    transform-style: preserve-3d;
    position: relative;
    z-index: 3;
}

.card-title {
    transition: transform 0.3s ease, color 0.3s ease;
    transform-style: preserve-3d;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-bright);
}

.card-content {
    color: var(--text-color);
}

.card-text {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.movie-genre {
    display: inline-block;
    background-color: rgba(229, 9, 20, 0.8);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.movie-card:hover .movie-genre {
    background-color: rgba(229, 9, 20, 1);
    box-shadow: 0 2px 8px rgba(229, 9, 20, 0.4);
}

.btn-detail {
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    margin-top: 5px;
}

.movie-card:hover .btn-detail {
    box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
}

/* Hiệu ứng typing text */
.hero-typing-text {
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    overflow: hidden;
    margin: 0 auto;
    animation: blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: var(--primary-color) }
}

/* Hiệu ứng nút sáng/tối */
.theme-toggle {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Hiệu ứng cho navbar */
.navbar-nav .nav-item {
    position: relative;
    margin: 0 5px;
}

.navbar-nav .nav-item::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.navbar-nav .nav-item:hover::before {
    width: 100%;
}

/* Hiệu ứng cho carousel */
.movie-carousel-wrapper {
    position: relative;
    overflow: hidden;
    padding: 20px 0;
    width: 100%;
    margin: 0 auto;
}

.movie-carousel-track {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.25, 1, 0.5, 1);
    width: 100%;
}

.movie-slide {
    flex: 0 0 auto;
    padding: 0 10px;
    transition: transform 0.3s ease, opacity 0.3s ease;
    width: 25%; /* Mặc định hiển thị 4 phim trên desktop */
}

@media (max-width: 992px) {
    .movie-slide {
        width: 33.333%; /* 3 phim trên tablet */
    }
}

@media (max-width: 768px) {
    .movie-slide {
        width: 50%; /* 2 phim trên mobile lớn */
    }
}

@media (max-width: 576px) {
    .movie-slide {
        width: 100%; /* 1 phim trên mobile nhỏ */
    }
}

.movie-container {
    padding: 10px;
    height: 100%;
}

.carousel-control-prev,
.carousel-control-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: background-color 0.3s ease, transform 0.3s ease;
    opacity: 0.7;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background-color: rgba(229, 9, 20, 0.9);
    transform: translateY(-50%) scale(1.1);
    opacity: 1;
}

.carousel-control-prev:active,
.carousel-control-next:active {
    transform: translateY(-50%) scale(0.95);
}

#prevBtn {
    left: 10px;
}

#nextBtn {
    right: 10px;
}

/* Hiệu ứng khi carousel đang chuyển động */
.movie-carousel-track.transitioning .movie-slide {
    pointer-events: none;
}

/* Hiệu ứng cho các phần tử trong hero section */
.hero-section .btn {
    transform: translateY(20px);
    opacity: 0;
    animation: fadeInUp 0.5s forwards;
    animation-delay: 1s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hiệu ứng cho hình ảnh */
.img-hover-zoom {
    overflow: hidden;
    border-radius: 8px;
}

.img-hover-zoom img {
    transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
    transform: scale(1.1);
}
