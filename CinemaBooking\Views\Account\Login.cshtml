@model CinemaBooking.Models.ViewModels.LoginViewModel

@{
    ViewData["Title"] = "Đăng nhập";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="auth-section">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h4>Đăng nhập vào CinemaBooking</h4>
                <p>Đặt vé xem phim dễ dàng và nhanh chóng</p>
            </div>
            <div class="auth-body">
                <form asp-action="Login" method="post" class="auth-form">
                    @if (!ViewData.ModelState.IsValid && ViewData.ModelState[""] != null && ViewData.ModelState[""].Errors.Count > 0)
                    {
                        <div class="alert alert-danger mb-3">
                            <span>@ViewData.ModelState[""].Errors[0].ErrorMessage</span>
                        </div>
                    }

                    <div class="form-floating mb-3">
                        <input type="text" asp-for="TenDangNhap" class="form-control" id="floatingInput" placeholder=" " required autocomplete="username" />
                        <label for="floatingInput">Tên đăng nhập hoặc Email</label>
                        <span asp-validation-for="TenDangNhap" class="text-danger"></span>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <input type="password" asp-for="MatKhau" class="form-control" id="floatingPassword" placeholder=" " required autocomplete="current-password" />
                        <label for="floatingPassword">Mật khẩu</label>
                        <span asp-validation-for="MatKhau" class="text-danger"></span>
                    </div>
                    
                    <div class="form-remember">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" asp-for="RememberMe" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>
                        <a asp-action="ForgotPassword" class="forgot-link">Quên mật khẩu?</a>
                    </div>
                    
                    <div class="mb-3 text-center">
                        <a href="/Account/GoogleLogin" class="btn btn-danger">
                            <i class="fab fa-google"></i> Đăng nhập bằng Google
                        </a>
                    </div>
                    
                    <button type="submit" class="btn-auth-submit">
                        Đăng nhập
                    </button>
                </form>
            </div>
            <div class="auth-footer">
                <p>Chưa có tài khoản? <a asp-action="Register">Đăng ký ngay</a></p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 