@{
    ViewData["Title"] = "Hướng dẫn Sandbox Test MoMo";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Hướng dẫn Test MoMo Sandbox</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Lưu ý:</strong> Đ<PERSON>y là hướng dẫn để test thanh toán MoMo trong môi trường Sandbox (thử nghiệm). Không sử dụng tài khoản MoMo thật cho các thao tác này.</p>
                    </div>

                    <h5 class="mt-4">1. <PERSON><PERSON><PERSON> bị</h5>
                    <ul>
                        <li>Đã cấu hình thông tin Sandbox MoMo trong <code>appsettings.json</code></li>
                        <li><PERSON><PERSON> tải ứng dụng MoMo Sandbox trên thiết bị di động</li>
                    </ul>

                    <h5 class="mt-4">2. Thông tin cấu hình hiện tại</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Thông số</th>
                                    <th>Giá trị</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Partner Code</td>
                                    <td><code>MOMOBKUN20180529</code></td>
                                </tr>
                                <tr>
                                    <td>Access Key</td>
                                    <td><code>klm05TvNBzhg7h7j</code></td>
                                </tr>
                                <tr>
                                    <td>Secret Key</td>
                                    <td><code>at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa</code></td>
                                </tr>
                                <tr>
                                    <td>API Endpoint</td>
                                    <td><code>https://test-payment.momo.vn/v2/gateway/api/create</code></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h5 class="mt-4">3. Tài khoản Sandbox</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Thông tin</th>
                                    <th>Giá trị</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Số điện thoại</td>
                                    <td><code>0909123123</code></td>
                                </tr>
                                <tr>
                                    <td>Mật khẩu</td>
                                    <td><code>000000</code></td>
                                </tr>
                                <tr>
                                    <td>Mã OTP</td>
                                    <td><code>000000</code></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h5 class="mt-4">4. Các bước test</h5>
                    <ol>
                        <li>Mở ứng dụng MoMo Sandbox trên thiết bị di động</li>
                        <li>Đăng nhập với số điện thoại và mật khẩu như trên</li>
                        <li>Trên website, chọn thanh toán qua MoMo khi đặt vé</li>
                        <li>Khi màn hình QR Code hiện lên, quét bằng ứng dụng MoMo Sandbox</li>
                        <li>Xác nhận thanh toán trong ứng dụng</li>
                        <li>Kiểm tra kết quả thanh toán trên website</li>
                    </ol>

                    <h5 class="mt-4">5. Xử lý lỗi phổ biến</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Mã lỗi</th>
                                    <th>Mô tả</th>
                                    <th>Giải pháp</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>98</td>
                                    <td>QR Code tạo không thành công</td>
                                    <td>
                                        <ul>
                                            <li>Kiểm tra chuỗi hash (signature)</li>
                                            <li>Đảm bảo đúng thứ tự các tham số trong hash</li>
                                            <li>Xem log để debug chi tiết</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td>8</td>
                                    <td>Lỗi kết nối MoMo</td>
                                    <td>Kiểm tra kết nối internet và cấu hình endpoint</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>Không đủ tiền trong ví</td>
                                    <td>Số dư ví Sandbox bị giới hạn, có thể cần reset</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h5 class="mt-4">6. Tìm kiếm lỗi</h5>
                    <p>Nếu gặp vấn đề, kiểm tra log tại <code>~/Logs/Payments/</code> để xem chi tiết request/response.</p>

                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Về trang chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 